/**
 * Test Google Antidetect System 2025
 * <PERSON><PERSON><PERSON> tra hệ thống antidetect mới nhất để bypass Google detection
 */

const { chromium } = require('playwright');
const AntidetectManager = require('./src/antidetect/antidetect-manager');
const HumanBehavior = require('./src/antidetect/human-behavior');

async function testGoogleAntidetect2025() {
  console.log('🧪 Testing Google Antidetect System 2025...\n');
  
  const antidetectManager = new AntidetectManager();
  let browser, context, page;
  
  try {
    // Load personas
    await antidetectManager.loadPersonas();
    
    // Get a random persona
    const persona = antidetectManager.getRandomPersona();
    console.log(`🎭 Using persona: ${persona.platform} - ${persona.userAgent.slice(0, 50)}...`);
    console.log(`   Screen: ${persona.screen.width}x${persona.screen.height}`);
    console.log(`   WebGL: ${persona.webgl.vendor}`);
    console.log(`   Timezone: ${persona.timezone}`);
    
    // Create browser with enhanced antidetect
    const launchOptions = antidetectManager.createBrowserLaunchOptions();
    browser = await chromium.launch({
      ...launchOptions,
      headless: false, // Show browser for testing
      devtools: false
    });
    
    // Create context with persona
    const contextOptions = await antidetectManager.createContextOptions(persona);
    context = await browser.newContext(contextOptions);
    
    // Create page
    page = await context.newPage();
    
    // Apply enhanced antidetect techniques
    await antidetectManager.applyAntidetectToPage(page, persona);
    
    // Apply Google-specific evasion
    await antidetectManager.applyGoogleEvasion(page, persona);
    
    // Initialize human behavior
    const humanBehavior = new HumanBehavior(page);
    humanBehavior.start();
    
    console.log('\n🔍 Testing Google Login Detection...');
    
    // Navigate to Google login page
    console.log('Navigating to Google login page...');
    try {
      await page.goto('https://accounts.google.com/signin', {
        waitUntil: 'networkidle',
        timeout: 30000
      });
      console.log('✅ Successfully loaded Google login page');
    } catch (error) {
      console.log(`❌ Failed to load Google login page: ${error.message}`);

      // Try to get current URL to see if redirected
      try {
        const currentUrl = page.url();
        console.log(`Current URL: ${currentUrl}`);

        if (currentUrl.includes('sorry') || currentUrl.includes('blocked')) {
          console.log('❌ Detected blocking/sorry page - antidetect may need improvement');
        }
      } catch (urlError) {
        console.log('❌ Cannot get current URL - page may be closed');
      }

      throw error;
    }
    
    // Add human delay before checking
    await antidetectManager.addHumanDelay(2000);

    // Check if page is still accessible
    try {
      await page.waitForLoadState('networkidle', { timeout: 10000 });
    } catch (error) {
      console.log('⚠️  Page load state check failed, continuing...');
    }

    // Check if browser/page is still open
    if (page.isClosed()) {
      console.log('❌ Page was closed - possible detection');
      return;
    }

    // Simulate realistic page interaction with error handling
    try {
      await humanBehavior.simulatePageInteraction();
    } catch (error) {
      console.log(`⚠️  Page interaction failed: ${error.message}`);
      // Continue with detection checks
    }
    
    // Check for bot detection messages
    const botDetectionSelectors = [
      'text=Không thể đăng nhập cho bạn',
      'text=Trình duyệt hoặc ứng dụng này có thể không an toàn',
      'text=This browser or app may not be secure',
      'text=Try using a different browser',
      'text=Hãy thử dùng trình duyệt khác',
      '[data-error-code]',
      '.error-message',
      '.warning-message',
      '[role="alert"]'
    ];
    
    let detectionFound = false;
    let detectionMessage = '';
    
    for (const selector of botDetectionSelectors) {
      try {
        const element = await page.locator(selector).first();
        if (await element.isVisible({ timeout: 3000 })) {
          detectionMessage = await element.textContent();
          console.log(`❌ Bot detection found: ${detectionMessage}`);
          detectionFound = true;
          break;
        }
      } catch (error) {
        // Selector not found, continue
      }
    }
    
    if (!detectionFound) {
      console.log('🎉 No bot detection messages found!');
      
      // Test form interaction
      console.log('\n🧪 Testing form interaction...');
      
      try {
        // Look for email input
        const emailSelectors = [
          'input[type="email"]',
          'input[name="identifier"]', 
          '#identifierId',
          '[data-initial-value]'
        ];
        
        let emailInput = null;
        for (const selector of emailSelectors) {
          try {
            emailInput = page.locator(selector);
            if (await emailInput.isVisible({ timeout: 2000 })) {
              console.log(`✅ Email input found: ${selector}`);
              break;
            }
          } catch (e) {
            continue;
          }
        }
        
        if (emailInput && await emailInput.isVisible()) {
          // Simulate realistic typing
          await humanBehavior.humanType(emailSelectors[2], '<EMAIL>');
          console.log('✅ Successfully typed in email field');
          
          // Add human delay
          await antidetectManager.addHumanDelay(1500);
          
          // Look for next button
          const nextSelectors = [
            '#identifierNext',
            '[data-primary-action-label]',
            'button[type="submit"]',
            '.VfPpkd-LgbsSe'
          ];
          
          let nextButton = null;
          for (const selector of nextSelectors) {
            try {
              nextButton = page.locator(selector);
              if (await nextButton.isVisible({ timeout: 2000 })) {
                console.log(`✅ Next button found: ${selector}`);
                break;
              }
            } catch (e) {
              continue;
            }
          }
          
          if (nextButton && await nextButton.isVisible()) {
            // Simulate human button click
            await humanBehavior.humanClickButton(nextSelectors[0]);
            console.log('✅ Successfully clicked next button');
            
            // Wait for response
            await page.waitForTimeout(5000);
            
            // Check for additional bot detection after interaction
            for (const selector of botDetectionSelectors) {
              try {
                const element = await page.locator(selector).first();
                if (await element.isVisible({ timeout: 3000 })) {
                  detectionMessage = await element.textContent();
                  console.log(`❌ Bot detection after interaction: ${detectionMessage}`);
                  detectionFound = true;
                  break;
                }
              } catch (error) {
                // Continue
              }
            }
            
            if (!detectionFound) {
              console.log('🎉 No bot detection after form interaction!');
            }
          } else {
            console.log('⚠️  Next button not found');
          }
        } else {
          console.log('⚠️  Email input not found');
        }
      } catch (error) {
        console.log(`⚠️  Form interaction test failed: ${error.message}`);
      }
    }
    
    // Test fingerprinting resistance
    console.log('\n🧪 Testing fingerprinting resistance...');
    
    const fingerprintTests = await page.evaluate(() => {
      const results = {};
      
      // Test WebGL fingerprinting
      try {
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
        if (gl) {
          results.webgl_vendor = gl.getParameter(gl.VENDOR);
          results.webgl_renderer = gl.getParameter(gl.RENDERER);
          results.webgl_version = gl.getParameter(gl.VERSION);
        }
      } catch (e) {
        results.webgl_error = e.message;
      }
      
      // Test Canvas fingerprinting
      try {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        ctx.textBaseline = 'top';
        ctx.font = '14px Arial';
        ctx.fillText('Antidetect test 🔒', 2, 2);
        results.canvas_fingerprint = canvas.toDataURL().slice(0, 50);
      } catch (e) {
        results.canvas_error = e.message;
      }
      
      // Test automation detection
      results.webdriver = window.navigator.webdriver;
      results.automation_controlled = window.chrome && window.chrome.runtime && window.chrome.runtime.onConnect;
      results.phantom = window.callPhantom || window._phantom;
      results.selenium = window._selenium || window.__webdriver_evaluate;
      results.nightmare = window.__nightmare;
      
      // Test user agent
      results.user_agent = navigator.userAgent;
      
      // Test screen properties
      results.screen_width = screen.width;
      results.screen_height = screen.height;
      results.screen_color_depth = screen.colorDepth;
      
      // Test language
      results.language = navigator.language;
      results.languages = navigator.languages;
      
      // Test timezone
      results.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      
      // Test hardware concurrency
      results.hardware_concurrency = navigator.hardwareConcurrency;
      
      // Test device memory
      results.device_memory = navigator.deviceMemory;
      
      return results;
    });
    
    console.log('\n📊 Fingerprint Test Results:');
    console.log(`  User Agent: ${fingerprintTests.user_agent?.slice(0, 80)}...`);
    console.log(`  WebGL Vendor: ${fingerprintTests.webgl_vendor || 'N/A'}`);
    console.log(`  WebGL Renderer: ${fingerprintTests.webgl_renderer || 'N/A'}`);
    console.log(`  Canvas Fingerprint: ${fingerprintTests.canvas_fingerprint || 'N/A'}`);
    console.log(`  Screen: ${fingerprintTests.screen_width}x${fingerprintTests.screen_height} (${fingerprintTests.screen_color_depth}bit)`);
    console.log(`  Language: ${fingerprintTests.language} (${fingerprintTests.languages?.join(', ')})`);
    console.log(`  Timezone: ${fingerprintTests.timezone}`);
    console.log(`  Hardware Concurrency: ${fingerprintTests.hardware_concurrency}`);
    console.log(`  Device Memory: ${fingerprintTests.device_memory}GB`);
    
    console.log('\n🔍 Automation Detection Results:');
    console.log(`  WebDriver detected: ${fingerprintTests.webdriver || 'false'}`);
    console.log(`  Automation controlled: ${fingerprintTests.automation_controlled || 'false'}`);
    console.log(`  Phantom detected: ${fingerprintTests.phantom || 'false'}`);
    console.log(`  Selenium detected: ${fingerprintTests.selenium || 'false'}`);
    console.log(`  Nightmare detected: ${fingerprintTests.nightmare || 'false'}`);
    
    // Final assessment
    console.log('\n📊 Final Assessment:');
    const automationDetected = fingerprintTests.webdriver || 
                              fingerprintTests.automation_controlled || 
                              fingerprintTests.phantom || 
                              fingerprintTests.selenium || 
                              fingerprintTests.nightmare;
    
    if (!detectionFound && !automationDetected) {
      console.log('🎉 EXCELLENT: Enhanced antidetect system is working perfectly!');
      console.log('   ✅ No bot detection messages found');
      console.log('   ✅ No automation indicators detected');
      console.log('   ✅ Fingerprinting appears to be working correctly');
      console.log('   ✅ Google login page loaded successfully');
    } else {
      console.log('⚠️  NEEDS IMPROVEMENT: Some detection methods are still active');
      if (detectionFound) {
        console.log(`   ❌ Bot detection message: ${detectionMessage}`);
      }
      if (automationDetected) {
        console.log('   ❌ Automation indicators detected');
      }
    }
    
    // Keep browser open for manual inspection
    console.log('\n🔍 Browser will remain open for manual inspection...');
    console.log('   - Try manually interacting with the page');
    console.log('   - Check for any additional detection methods');
    console.log('   - Press Ctrl+C to close when done');
    
    // Wait for user to close
    process.on('SIGINT', async () => {
      console.log('\n👋 Closing browser...');
      if (browser) {
        await browser.close();
      }
      process.exit(0);
    });
    
    // Wait indefinitely
    await new Promise(() => {});
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    if (browser) {
      await browser.close();
    }
  }
}

// Run the test
if (require.main === module) {
  testGoogleAntidetect2025().catch(console.error);
}

module.exports = { testGoogleAntidetect2025 };
