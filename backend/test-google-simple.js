/**
 * Simple Google Antidetect Test
 * Test cơ bản để kiểm tra Google detection
 */

const { chromium } = require('playwright');
const AntidetectManager = require('./src/antidetect/antidetect-manager');

async function testGoogleSimple() {
  console.log('🧪 Simple Google Antidetect Test...\n');
  
  const antidetectManager = new AntidetectManager();
  let browser, context, page;
  
  try {
    // Load personas
    await antidetectManager.loadPersonas();
    const persona = antidetectManager.getRandomPersona();
    console.log(`🎭 Using persona: ${persona.platform} - ${persona.userAgent.slice(0, 60)}...`);
    
    // Create browser
    const launchOptions = antidetectManager.createBrowserLaunchOptions();
    browser = await chromium.launch({
      ...launchOptions,
      headless: false
    });
    
    // Create context
    const contextOptions = await antidetectManager.createContextOptions(persona);
    context = await browser.newContext(contextOptions);
    page = await context.newPage();
    
    // Apply antidetect
    await antidetectManager.applyAntidetectToPage(page, persona);
    await antidetectManager.applyGoogleEvasion(page, persona);
    
    console.log('\n🔍 Testing Google login page...');
    
    // Navigate to Google
    await page.goto('https://accounts.google.com/signin', { 
      waitUntil: 'domcontentloaded',
      timeout: 30000 
    });
    
    console.log('✅ Page loaded successfully');
    
    // Wait a bit
    await page.waitForTimeout(3000);
    
    // Check for detection messages
    const detectionSelectors = [
      'text=Không thể đăng nhập cho bạn',
      'text=This browser or app may not be secure',
      'text=Trình duyệt hoặc ứng dụng này có thể không an toàn'
    ];
    
    let detected = false;
    for (const selector of detectionSelectors) {
      try {
        if (await page.locator(selector).isVisible({ timeout: 2000 })) {
          console.log(`❌ Detection found: ${selector}`);
          detected = true;
          break;
        }
      } catch (e) {
        // Continue
      }
    }
    
    if (!detected) {
      console.log('🎉 No initial detection found!');
      
      // Try simple interaction
      try {
        const emailInput = page.locator('#identifierId');
        if (await emailInput.isVisible({ timeout: 5000 })) {
          console.log('\n🧪 Testing form interaction...');
          
          // Type slowly
          await emailInput.click();
          await page.waitForTimeout(1000);
          await emailInput.type('<EMAIL>', { delay: 100 });
          await page.waitForTimeout(1000);
          
          // Click next
          const nextBtn = page.locator('#identifierNext');
          if (await nextBtn.isVisible()) {
            await nextBtn.click();
            await page.waitForTimeout(5000);
            
            // Check again for detection
            for (const selector of detectionSelectors) {
              try {
                if (await page.locator(selector).isVisible({ timeout: 3000 })) {
                  console.log(`❌ Detection after interaction: ${selector}`);
                  detected = true;
                  break;
                }
              } catch (e) {
                // Continue
              }
            }
            
            if (!detected) {
              console.log('🎉 No detection after interaction!');
            }
          }
        }
      } catch (error) {
        console.log(`⚠️  Interaction test failed: ${error.message}`);
      }
    }
    
    // Final result
    if (!detected) {
      console.log('\n✅ SUCCESS: Antidetect system working well!');
    } else {
      console.log('\n❌ DETECTED: Need further improvements');
    }
    
    console.log('\n🔍 Browser will stay open for manual inspection...');
    console.log('Press Ctrl+C to close');
    
    // Keep open
    await new Promise(() => {});
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    // Cleanup handled by Ctrl+C
  }
}

// Handle Ctrl+C
process.on('SIGINT', async () => {
  console.log('\n👋 Closing...');
  process.exit(0);
});

if (require.main === module) {
  testGoogleSimple().catch(console.error);
}

module.exports = { testGoogleSimple };
