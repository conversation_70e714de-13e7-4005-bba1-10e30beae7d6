{"net": {"http_server_properties": {"broken_alternative_services": [{"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 1, "broken_until": "**********", "host": "accounts.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "broken_count": 1, "broken_until": "**********", "host": "storage.googleapis.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 1, "broken_until": "**********", "host": "www.gstatic.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 1, "broken_until": "**********", "host": "content-autofill.googleapis.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 1, "broken_until": "**********", "host": "play.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 2, "broken_until": "**********", "host": "android.clients.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 1, "broken_until": "1751444062", "host": "signaler-pa.googleapis.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 1, "broken_until": "1751444094", "host": "lh3.googleusercontent.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 1, "broken_until": "1751444095", "host": "fonts.gstatic.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 4, "broken_until": "1751444228", "host": "www.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "broken_count": 3, "broken_until": "1751444237", "host": "whatismyipaddress.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "broken_count": 1, "broken_until": "**********", "host": "fonts.googleapis.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "broken_count": 1, "broken_until": "**********", "host": "app.fusebox.fm", "port": 443, "protocol_str": "quic"}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "broken_count": 1, "broken_until": "**********", "host": "fonts.gstatic.com", "port": 443, "protocol_str": "quic"}], "servers": [{"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", true, 0], "server": "https://accounts.youtube.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://lf16-tiktok-common.ibytedtos.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://p16-sign-useast2a.tiktokcdn.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://signaler-pa.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABodHRwczovL2dvb2dsZS5jb20udm4AAAA=", false, 0], "server": "https://accounts.google.com.vn", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://play.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACwAAABodHRwczovL3Bhc3N3b3Jkc2xlYWtjaGVjay1wYS5nb29nbGVhcGlzLmNvbQ==", false, 0], "server": "https://passwordsleakcheck-pa.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://accounts.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL2h0dHBiaW4ub3JnAA==", false, 0], "server": "https://httpbin.org", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://sf16-website.neutral.ttwstatic.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://login-no1a.www.tiktok.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://web-va.tiktok.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://us.tiktok.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://libraweb-va.tiktok.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://p16-pu-sign-useast8.tiktokcdn-us.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://www.tiktokw.us", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://v16-webapp-prime.tiktok.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://webcast.tiktok.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://p16-sign-va.tiktokcdn.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://p16.tiktokcdn-us.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://starling-va.tiktokv.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398509530224017", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://android.clients.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398509530751576", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398509531058011", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://lh3.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://v19-webapp-prime.tiktok.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398509532956740", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://apis.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398509533234076", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://lh3.googleusercontent.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398509533313413", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://ogads-pa.clients6.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://sf16-sg.tiktokcdn.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398509535156705", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://play.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398509535567275", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://lh3.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398509535662637", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://fonts.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398509395302380", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://lh3.googleusercontent.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://www.googleadservices.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398509396731134", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398509537748504", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://www.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396003941004665", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://a.pub.network", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://cmp.inmobi.com", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://map.whatismyipaddress.info", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://a.omappapi.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396003940964699", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://cdn.onesignal.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396003941697870", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://app.fusebox.fm", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398509541728976", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://fonts.googleapis.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://www.tiktok.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398509542282964", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://fonts.gstatic.com", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://d.pub.network", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398509542450953", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://storage.googleapis.com", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://api.omappapi.com", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396003940167791", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://whatismyipaddress.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398509540723855", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://www.googletagmanager.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://sf16-website-login.neutral.ttwstatic.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://mon.tiktokv.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://mcs-va.tiktokv.com", "supports_spdy": true}], "version": 5}, "network_qualities": {"CAASABiAgICA+P////8B": "3G"}}}