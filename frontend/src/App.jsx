import React, { useState, useEffect } from 'react'
import WebSocketClient from './api/websocket-client'
import AccountTable from './components/AccountTable'
import SettingsPanel from './components/SettingsPanel'
import LogViewer from './components/LogViewer'
import './styles/App.css'

function App() {
  const [wsClient] = useState(() => new WebSocketClient());
  const [isConnected, setIsConnected] = useState(false);
  const [accounts, setAccounts] = useState([]);
  const [settings, setSettings] = useState({});
  const [logs, setLogs] = useState([]);

  // Counter để tạo unique log IDs
  const logIdCounter = React.useRef(0);

  useEffect(() => {
    // Kết nối đến backend
    wsClient.connect()
      .then(() => {
        setIsConnected(true);
        // Load dữ liệu ban đầu
        wsClient.getAccounts();
        wsClient.getSettings();
      })
      .catch(error => {
        console.error('Failed to connect to backend:', error);
      });

    // Đăng ký event handlers
    wsClient.on('connection', (message) => {
      setIsConnected(message.status === 'connected');
    });

    wsClient.on('success', (message) => {
      if (message.data) {
        if (message.data.accounts) {
          setAccounts(message.data.accounts);
        }
        if (message.data.settings) {
          setSettings(message.data.settings);
        }
      }

      // Thêm vào logs
      setLogs(prev => [...prev, {
        id: `${Date.now()}-${++logIdCounter.current}`,
        level: 'success',
        message: message.message,
        timestamp: message.timestamp
      }]);
    });

    wsClient.on('error', (message) => {
      setLogs(prev => [...prev, {
        id: `${Date.now()}-${++logIdCounter.current}`,
        level: 'error',
        message: message.message,
        timestamp: message.timestamp
      }]);
    });

    wsClient.on('log', (message) => {
      setLogs(prev => [...prev, {
        id: `${Date.now()}-${++logIdCounter.current}`,
        level: message.level,
        message: message.message,
        accountId: message.accountId,
        timestamp: message.timestamp
      }]);
    });

    wsClient.on('account_status_update', (message) => {
      setAccounts(prev => prev.map(account =>
        account.id === message.accountId
          ? { ...account, status: message.status, ...message }
          : account
      ));
    });

    // Cleanup khi component unmount
    return () => {
      wsClient.disconnect();
    };
  }, [wsClient]);

  const handleLoadAccounts = () => {
    wsClient.loadAccounts();
  };

  const handleLoadProxies = () => {
    wsClient.loadProxies();
  };

  const handleLoadComments = () => {
    wsClient.loadComments();
  };

  const handleUpdateSettings = (newSettings) => {
    wsClient.updateSettings(newSettings);
    setSettings(newSettings);
  };

  const handleStartAutomation = (accountIds, targetProfile) => {
    wsClient.startAutomation(accountIds, targetProfile);
  };

  const handleStopAutomation = () => {
    wsClient.stopAutomation();
  };

  const handlePauseAutomation = () => {
    wsClient.pauseAutomation();
  };

  return (
    <div className="App">
      <header className="app-header">
        <h1>TikTok Automation Dashboard</h1>
        <div className="connection-status">
          <span className={`status-indicator ${isConnected ? 'connected' : 'disconnected'}`}>
            {isConnected ? '🟢 Connected' : '🔴 Disconnected'}
          </span>
        </div>
      </header>

      <div className="app-content">
        <div className="left-panel">
          <AccountTable
            accounts={accounts}
            onRefresh={() => wsClient.getAccounts()}
            wsClient={wsClient}
          />
        </div>

        <div className="right-panel">
          <SettingsPanel
            settings={settings}
            accounts={accounts}
            onLoadAccounts={handleLoadAccounts}
            onLoadProxies={handleLoadProxies}
            onLoadComments={handleLoadComments}
            onUpdateSettings={handleUpdateSettings}
            onStartAutomation={handleStartAutomation}
            onStopAutomation={handleStopAutomation}
            onPauseAutomation={handlePauseAutomation}
            isConnected={isConnected}
          />

          <LogViewer
            logs={logs}
            onClearLogs={() => setLogs([])}
          />
        </div>
      </div>
    </div>
  )
}

export default App
